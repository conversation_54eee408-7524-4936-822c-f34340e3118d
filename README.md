# 🎬 Video to Audio Converter

This simple Python program converts video files (such as `.mp4`) to MP3 audio files using the [moviepy](https://zulko.github.io/moviepy/) library.

## 📦 Features

- 🎥 Converts video files to high-quality MP3 audio
- 🐍 Easy to use from the command line
- 💾 Keeps your original video file intact

## 🚀 Setup

1. **Clone or download this repository.**

2. **Install Python 3** if you haven't already.

3. **Upgrade pip (optional but recommended):**
   ```sh
   python -m pip install --upgrade pip
   ```

4. **Install dependencies:**
   ```sh
   pip install -r requirements.txt
   ```

## 🎉 Usage

To convert a video file (e.g., `video-1.mp4`) to MP3:

```sh
python converter.py video-1.mp4
```

- The program will create `video-1.mp3` in the same directory.
- If you do not provide a file, it will prompt you for one.

## 📝 Notes

- Only tested with `.mp4` files, but other formats supported by moviepy may work.
- Make sure your video file is in the same directory or provide the full path.

## 🛠️ Troubleshooting

- If you see an error about `moviepy` not being installed, run:
  ```sh
  pip install moviepy
  ```

## 📄 License

MIT License

---

Enjoy converting your videos to