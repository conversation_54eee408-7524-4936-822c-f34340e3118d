
try:
    import moviepy.editor as mp
except ImportError:
    print("Error: moviepy is not installed. Run 'pip install moviepy' to install it.")
    sys.exit(1)

import sys

def convert_to_mp3(video_path):
    """
    Converts a video file to an MP3 audio file.

    Args:
        video_path (str): The path to the video file.
    """
    try:
        video_clip = mp.VideoFileClip(video_path)
        audio_clip = video_clip.audio
        mp3_path = video_path.rsplit('.', 1)[0] + '.mp3'
        audio_clip.write_audiofile(mp3_path)
        audio_clip.close()
        video_clip.close()
        print(f"Successfully converted {video_path} to {mp3_path}")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        video_file = sys.argv[1]
        convert_to_mp3(video_file)
    else:
        print("Please provide the path to a video file.")

